/**
 * Grid Integration Patch for SnapDatepicker
 * This file provides the necessary modifications to integrate custom datepickers
 * into the SnapGrid component, replacing native date inputs.
 * 
 * Usage: Include this file after snap-grid.js and snap-datepicker.js
 */

(function() {
    'use strict';

    // Store original SnapGrid prototype methods that we'll modify
    const originalCreateColumnMenu = SnapGrid.prototype.createColumnMenu;
    const originalUpdateFilterInputs = SnapGrid.prototype.updateFilterInputs;
    const originalCreateFilterTab = SnapGrid.prototype.createFilterTab;

    /**
     * Enhanced createColumnMenu with custom datepicker support and dropdown change listener
     */
    SnapGrid.prototype.createColumnMenu = function(column) {
        // Call original method
        const menu = originalCreateColumnMenu.call(this, column);

        // Add custom datepicker initialization for date columns
        if (column.type === 'date') {
            // Wait for menu to be added to DOM, then initialize datepickers
            setTimeout(() => {
                this.initializeDatepickersInMenu(menu, column);

                // Add listener for dropdown changes that might create date inputs
                const firstDropdown = menu.querySelector('.snap-dropdown');
                if (firstDropdown) {
                    // Store reference to grid instance
                    const gridInstance = this;

                    // Override the dropdown change event to handle date input conversion
                    const originalChangeHandler = firstDropdown.onchange;
                    firstDropdown.addEventListener('change', function() {
                        // Call original handler first
                        if (originalChangeHandler) {
                            originalChangeHandler.call(this);
                        }

                        // Wait for DOM updates, then convert any new date inputs
                        setTimeout(() => {
                            gridInstance.convertDynamicDateInputs(menu);
                        }, 50);
                    });
                }
            }, 0);
        }

        return menu;
    };

    /**
     * Initialize custom datepickers in column menu
     */
    SnapGrid.prototype.initializeDatepickersInMenu = function(menu, column) {
        const dateInputs = menu.querySelectorAll('input[type="date"]');

        dateInputs.forEach(input => {
            // Skip if already converted
            if (input._snapDatepicker || input.style.display === 'none') {
                return;
            }

            // Create wrapper for the datepicker
            const wrapper = document.createElement('div');
            wrapper.className = 'snap-grid-datepicker-wrapper';

            // Insert wrapper
            input.parentNode.insertBefore(wrapper, input);

            // Determine placeholder based on input
            let placeholder = 'Select date';
            if (input.placeholder) {
                placeholder = input.placeholder;
            } else if (input.classList.contains('first-filter-input')) {
                placeholder = 'From date';
            } else if (input.classList.contains('second-filter-input')) {
                placeholder = 'To date';
            }

            // Create custom datepicker
            const datepicker = new SnapDatepicker(wrapper, {
                value: input.value,
                placeholder: placeholder,
                closeOnSelect: true
            });

            // Sync values and events
            datepicker.on('change', (value) => {
                input.value = value;

                // Trigger change event on original input
                const changeEvent = new Event('change', { bubbles: true });
                input.dispatchEvent(changeEvent);

                // Update filter if this is a filter input
                if (input.classList.contains('filter-input')) {
                    this.handleFilterInputChange(column, input);
                }
            });

            // Hide original input
            input.style.display = 'none';

            // Store reference
            input._snapDatepicker = datepicker;
        });
    };

    /**
     * Enhanced updateFilterInputs with custom datepicker support
     */
    SnapGrid.prototype.updateFilterInputs = function(column, selectedOperator, firstInput, secondInput, firstFilterWrapper, secondFilterWrapper, logicSection, secondDropdown) {
        // Call original method first
        if (originalUpdateFilterInputs) {
            originalUpdateFilterInputs.call(this, column, selectedOperator, firstInput, secondInput, firstFilterWrapper, secondFilterWrapper, logicSection, secondDropdown);
        }

        const isDateOnly = column.type === 'date';

        if (isDateOnly) {
            // Convert native date inputs to custom datepickers
            setTimeout(() => {
                this.convertDateInputsToCustom(firstInput, secondInput, selectedOperator);
            }, 0);
        }
    };



    /**
     * Convert native date inputs to custom datepickers
     */
    SnapGrid.prototype.convertDateInputsToCustom = function(firstInput, secondInput, selectedOperator) {
        // Convert first input if it's a date type
        if (firstInput && firstInput.type === 'date' && !firstInput._snapDatepicker) {
            this.convertSingleDateInput(firstInput);
        }

        // Convert second input if it's a date type and visible
        if (secondInput && secondInput.type === 'date' && !secondInput._snapDatepicker &&
            secondInput.parentElement.style.display !== 'none') {
            this.convertSingleDateInput(secondInput);
        }
    };

    /**
     * Convert dynamically created date inputs (for dropdown changes)
     */
    SnapGrid.prototype.convertDynamicDateInputs = function(container) {
        console.log('🔍 Looking for dynamic date inputs in container:', container);
        const dateInputs = container.querySelectorAll('input[type="date"]:not([data-converted])');

        console.log('📅 Found date inputs to convert:', dateInputs.length);

        dateInputs.forEach(input => {
            if (!input._snapDatepicker) {
                this.convertSingleDateInput(input);
                input.setAttribute('data-converted', 'true');
            }
        });

        // Also set up a MutationObserver to catch type changes
        if (!container._dateInputObserver) {
            const gridInstance = this;
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'type') {
                        const input = mutation.target;
                        if (input.type === 'date' && !input._snapDatepicker && !input.hasAttribute('data-converted')) {
                            console.log('🔄 Detected type change to date, converting:', input);
                            gridInstance.convertSingleDateInput(input);
                            input.setAttribute('data-converted', 'true');
                        }
                    }
                });
            });

            observer.observe(container, {
                attributes: true,
                attributeFilter: ['type'],
                subtree: true
            });

            container._dateInputObserver = observer;
        }
    };

    /**
     * Convert a single date input to custom datepicker
     */
    SnapGrid.prototype.convertSingleDateInput = function(input) {
        if (!input || input._snapDatepicker) return;

        console.log('🔄 Converting date input to custom datepicker:', input);

        // CRITICAL: Completely prevent any native date behavior
        input.type = 'text'; // Ensure it's text type
        input.setAttribute('type', 'text'); // Force attribute
        input.removeAttribute('data-date'); // Remove any date attributes

        // Disable native date picker completely
        input.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            // Focus the custom datepicker instead
            if (this._snapDatepicker) {
                this._snapDatepicker.open();
            }
        });

        input.addEventListener('focus', function(e) {
            e.preventDefault();
            e.stopPropagation();
            // Focus the custom datepicker instead
            if (this._snapDatepicker) {
                this._snapDatepicker.open();
            }
            this.blur(); // Remove focus from native input
        });

        // Create wrapper
        const wrapper = document.createElement('div');
        wrapper.className = 'snap-grid-datepicker-wrapper';
        wrapper.style.position = 'relative';
        wrapper.style.width = '100%';

        // Insert wrapper
        input.parentNode.insertBefore(wrapper, input);

        // Create datepicker
        const datepicker = new SnapDatepicker(wrapper, {
            value: input.value,
            placeholder: input.placeholder || 'Select date',
            closeOnSelect: true
        });

        // Sync values
        datepicker.on('change', (value) => {
            input.value = value;
            const changeEvent = new Event('change', { bubbles: true });
            input.dispatchEvent(changeEvent);
        });

        // COMPLETELY hide and disable the original input
        input.style.display = 'none';
        input.style.position = 'absolute';
        input.style.left = '-9999px';
        input.style.top = '-9999px';
        input.style.width = '0';
        input.style.height = '0';
        input.style.opacity = '0';
        input.style.pointerEvents = 'none';
        input.tabIndex = -1;
        input.setAttribute('aria-hidden', 'true');

        input._snapDatepicker = datepicker;

        console.log('✅ Date input converted successfully, native input completely disabled');
    };

    /**
     * Handle filter input changes for custom datepickers
     */
    SnapGrid.prototype.handleFilterInputChange = function(column, input) {
        // This method can be called when datepicker values change
        // to trigger filter updates in the grid
        console.log('📅 Custom datepicker filter change:', {
            column: column.field,
            value: input.value
        });
    };

    /**
     * Cleanup method for custom datepickers
     */
    SnapGrid.prototype.cleanupCustomDatepickers = function(container) {
        const dateInputs = container.querySelectorAll('input[type="date"]');
        
        dateInputs.forEach(input => {
            if (input._snapDatepicker) {
                input._snapDatepicker.destroy();
                input.style.display = '';
                delete input._snapDatepicker;
            }
        });
    };

    // Add CSS for grid-specific datepicker styling
    const gridDatepickerCSS = `
        .snap-grid-datepicker-wrapper {
            width: 100%;
        }
        
        .snap-grid-datepicker-wrapper .snap-datepicker-input {
            font-size: 12px;
            padding: 8px 32px 8px 8px;
            height: 32px;
            box-sizing: border-box;
        }
        
        .snap-grid-datepicker-wrapper .snap-datepicker-icon {
            right: 8px;
        }
        
        .snap-grid-datepicker-wrapper .snap-datepicker-dropdown {
            font-size: 12px;
            z-index: 10000;
        }
        
        /* Ensure datepicker dropdowns appear above grid menus */
        .snap-grid .snap-datepicker-dropdown {
            z-index: 10001;
        }
    `;

    // Inject CSS
    const style = document.createElement('style');
    style.textContent = gridDatepickerCSS;
    document.head.appendChild(style);

    // Auto-initialize when SnapGrid is used
    const originalSnapGridConstructor = window.SnapGrid;
    
    window.SnapGrid = function(container, options) {
        // Call original constructor
        const instance = new originalSnapGridConstructor(container, options);
        
        // Add initialization hook for custom datepickers
        const originalRender = instance.render;
        instance.render = function() {
            const result = originalRender.call(this);
            
            // Initialize custom datepickers after render
            setTimeout(() => {
                if (window.SnapDatepicker) {
                    this.initializeCustomDatepickers();
                }
            }, 100);
            
            return result;
        };
        
        // Add method to initialize custom datepickers
        instance.initializeCustomDatepickers = function() {
            const container = this.container;
            const dateInputs = container.querySelectorAll('input[type="date"]:not([data-converted])');
            
            dateInputs.forEach(input => {
                if (!input._snapDatepicker) {
                    DatepickerIntegration.replaceNativeInput(input);
                    input.setAttribute('data-converted', 'true');
                }
            });
        };
        
        return instance;
    };

    // Copy static properties
    Object.setPrototypeOf(window.SnapGrid, originalSnapGridConstructor);
    Object.assign(window.SnapGrid, originalSnapGridConstructor);

    /**
     * Override createFilterTab to intercept dropdown change events
     */
    SnapGrid.prototype.createFilterTab = function(column) {
        // Call original method
        const panel = originalCreateFilterTab.call(this, column);

        // If this is a date column, override the dropdown change behavior
        if (column.type === 'date') {
            setTimeout(() => {
                this.overrideDateDropdownBehavior(panel, column);
            }, 0);
        }

        return panel;
    };

    /**
     * Override dropdown change behavior for date columns
     */
    SnapGrid.prototype.overrideDateDropdownBehavior = function(panel, column) {
        const firstDropdown = panel.querySelector('.snap-dropdown');

        if (!firstDropdown) {
            console.warn('⚠️ Could not find dropdown for date input override');
            return;
        }

        // Find the original change event listeners
        const originalListeners = [];

        // Clone the dropdown to remove all event listeners
        const newDropdown = firstDropdown.cloneNode(true);
        firstDropdown.parentNode.replaceChild(newDropdown, firstDropdown);

        // Add our custom change handler
        newDropdown.addEventListener('change', () => {
            const selectedOperator = this.getCustomDropdownValue(newDropdown);
            const firstInput = panel.querySelector('.filter-input-with-icon');
            const secondInput = panel.querySelector('.filter-input-wrapper:last-of-type .filter-input-with-icon');
            const firstFilterWrapper = panel.querySelector('.filter-input-wrapper');
            const secondFilterWrapper = panel.querySelector('.filter-input-wrapper:last-of-type');
            const logicSection = panel.querySelector('.filter-logic-section');
            const secondDropdown = panel.querySelector('.snap-dropdown:last-of-type');

            console.log('📅 Custom dropdown change handler for date column:', selectedOperator);

            // Handle "In range" selection for date columns
            if (selectedOperator === 'inRange') {
                logicSection.style.display = 'none';
                secondDropdown.style.display = 'none';
                firstFilterWrapper.style.display = 'flex';
                secondFilterWrapper.style.display = 'flex';

                // Instead of setting type to 'date', create custom datepickers
                firstInput.placeholder = 'From date';
                secondInput.placeholder = 'To date';

                console.log('🔄 Creating custom datepickers for In range mode');

                // Create custom datepickers instead of native date inputs
                setTimeout(() => {
                    this.createCustomDatepickerForInput(firstInput, 'From date');
                    this.createCustomDatepickerForInput(secondInput, 'To date');
                }, 0);
            }
            // Handle date comparison operators
            else if (['equals', 'notEquals', 'lessThan', 'greaterThan'].includes(selectedOperator)) {
                firstFilterWrapper.style.display = 'flex';
                secondFilterWrapper.style.display = 'none';
                firstInput.placeholder = 'Select date';
                logicSection.style.display = 'none';
                secondDropdown.style.display = 'none';

                console.log('🔄 Creating custom datepicker for comparison operator:', selectedOperator);

                // Create custom datepicker instead of native date input
                setTimeout(() => {
                    this.createCustomDatepickerForInput(firstInput, 'Select date');
                }, 0);
            }
            // Handle other operators (predefined ranges)
            else {
                firstFilterWrapper.style.display = 'none';
                secondFilterWrapper.style.display = 'none';
                logicSection.style.display = 'none';
                secondDropdown.style.display = 'none';
            }

            // Apply the filter
            this.applyFilterFromTab(panel, column);
        });
    };

    /**
     * Create custom datepicker for an input without setting type to 'date'
     */
    SnapGrid.prototype.createCustomDatepickerForInput = function(input, placeholder) {
        if (!input || input._snapDatepicker) return;

        console.log('🔄 Creating custom datepicker for input:', input, 'placeholder:', placeholder);

        // CRITICAL: Completely prevent any native date behavior
        input.type = 'text'; // Ensure it's text type
        input.setAttribute('type', 'text'); // Force attribute
        input.removeAttribute('data-date'); // Remove any date attributes

        // Disable native date picker completely
        input.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            // Focus the custom datepicker instead
            if (this._snapDatepicker) {
                this._snapDatepicker.open();
            }
        });

        input.addEventListener('focus', function(e) {
            e.preventDefault();
            e.stopPropagation();
            // Focus the custom datepicker instead
            if (this._snapDatepicker) {
                this._snapDatepicker.open();
            }
            this.blur(); // Remove focus from native input
        });

        // Create wrapper for the datepicker that will replace the input visually
        const wrapper = document.createElement('div');
        wrapper.className = 'snap-grid-datepicker-wrapper';
        wrapper.style.position = 'relative';
        wrapper.style.width = '100%';

        // Insert wrapper in the exact same position as the input
        input.parentNode.insertBefore(wrapper, input);

        // Create custom datepicker
        const datepicker = new SnapDatepicker(wrapper, {
            value: input.value,
            placeholder: placeholder,
            closeOnSelect: true
        });

        // Sync values and events
        datepicker.on('change', (value) => {
            input.value = value;

            // Trigger change event on original input
            const changeEvent = new Event('change', { bubbles: true });
            input.dispatchEvent(changeEvent);
        });

        // COMPLETELY hide and disable the original input
        input.style.display = 'none';
        input.style.position = 'absolute';
        input.style.left = '-9999px';
        input.style.top = '-9999px';
        input.style.width = '0';
        input.style.height = '0';
        input.style.opacity = '0';
        input.style.pointerEvents = 'none';
        input.tabIndex = -1;
        input.setAttribute('aria-hidden', 'true');

        // Store reference
        input._snapDatepicker = datepicker;

        console.log('✅ Custom datepicker created successfully, native input completely disabled');
    };

    /**
     * Apply filter from tab (simplified version of original logic)
     */
    SnapGrid.prototype.applyFilterFromTab = function(panel, column) {
        const firstDropdown = panel.querySelector('.snap-dropdown');
        const firstInput = panel.querySelector('.filter-input-with-icon');
        const secondInput = panel.querySelector('.filter-input-wrapper:last-of-type .filter-input-with-icon');

        const firstOperator = this.getCustomDropdownValue(firstDropdown);

        if (firstOperator === 'inRange') {
            // Date range: use From/To date inputs
            const fromDate = firstInput.value;
            const toDate = secondInput.value;

            if (fromDate || toDate) {
                console.log('📅 Applying date range filter:', { fromDate, toDate });
                this.setFilter(column.field, {
                    type: this.getFilterType(column),
                    operator: 'inRange',
                    value: { fromValue: fromDate, toValue: toDate }
                });
            } else {
                console.log('📅 Clearing date range filter - no dates provided');
                this.clearFilter(column.field);
            }
        } else if (['equals', 'notEquals', 'lessThan', 'greaterThan'].includes(firstOperator)) {
            // Date comparison operators: use single date input
            const dateValue = firstInput.value;

            if (dateValue) {
                console.log('📅 Applying date comparison filter:', { operator: firstOperator, date: dateValue });
                this.setFilter(column.field, {
                    type: this.getFilterType(column),
                    operator: firstOperator,
                    value: dateValue
                });
            } else {
                console.log('📅 Clearing date comparison filter - no date provided');
                this.clearFilter(column.field);
            }
        } else if (firstOperator && firstOperator !== 'pleaseSelect') {
            // Predefined date range (Today, Yesterday, etc.)
            console.log('📅 Applying predefined date filter:', firstOperator);
            this.setFilter(column.field, {
                type: this.getFilterType(column),
                operator: firstOperator,
                value: null // No input value needed for preset date ranges
            });
        } else {
            console.log('📅 Clearing date filter');
            this.clearFilter(column.field);
        }
    };

    console.log('✅ SnapGrid custom datepicker integration loaded with native input interception');

})();
