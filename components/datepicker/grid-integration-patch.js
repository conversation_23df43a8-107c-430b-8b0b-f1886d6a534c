/**
 * Grid Integration Patch for SnapDatepicker
 * This file provides the necessary modifications to integrate custom datepickers
 * into the SnapGrid component, replacing native date inputs.
 * 
 * Usage: Include this file after snap-grid.js and snap-datepicker.js
 */

(function() {
    'use strict';

    // Store original SnapGrid prototype methods that we'll modify
    const originalCreateColumnMenu = SnapGrid.prototype.createColumnMenu;
    const originalUpdateFilterInputs = SnapGrid.prototype.updateFilterInputs;

    /**
     * Enhanced createColumnMenu with custom datepicker support
     */
    SnapGrid.prototype.createColumnMenu = function(column) {
        // Call original method
        const menu = originalCreateColumnMenu.call(this, column);
        
        // Add custom datepicker initialization for date columns
        if (column.type === 'date') {
            // Wait for menu to be added to DOM, then initialize datepickers
            setTimeout(() => {
                this.initializeDatepickersInMenu(menu, column);
            }, 0);
        }
        
        return menu;
    };

    /**
     * Initialize custom datepickers in column menu
     */
    SnapGrid.prototype.initializeDatepickersInMenu = function(menu, column) {
        const dateInputs = menu.querySelectorAll('input[type="date"]');
        
        dateInputs.forEach(input => {
            // Skip if already converted
            if (input._snapDatepicker || input.style.display === 'none') {
                return;
            }

            // Create wrapper for the datepicker
            const wrapper = document.createElement('div');
            wrapper.className = 'snap-grid-datepicker-wrapper';
            
            // Insert wrapper
            input.parentNode.insertBefore(wrapper, input);
            
            // Determine placeholder based on input
            let placeholder = 'Select date';
            if (input.placeholder) {
                placeholder = input.placeholder;
            } else if (input.classList.contains('first-filter-input')) {
                placeholder = 'From date';
            } else if (input.classList.contains('second-filter-input')) {
                placeholder = 'To date';
            }

            // Create custom datepicker
            const datepicker = new SnapDatepicker(wrapper, {
                value: input.value,
                placeholder: placeholder,
                closeOnSelect: true
            });

            // Sync values and events
            datepicker.on('change', (value) => {
                input.value = value;
                
                // Trigger change event on original input
                const changeEvent = new Event('change', { bubbles: true });
                input.dispatchEvent(changeEvent);
                
                // Update filter if this is a filter input
                if (input.classList.contains('filter-input')) {
                    this.handleFilterInputChange(column, input);
                }
            });

            // Hide original input
            input.style.display = 'none';
            
            // Store reference
            input._snapDatepicker = datepicker;
        });
    };

    /**
     * Enhanced updateFilterInputs with custom datepicker support
     */
    SnapGrid.prototype.updateFilterInputs = function(column, selectedOperator, firstInput, secondInput, firstFilterWrapper, secondFilterWrapper, logicSection, secondDropdown) {
        // Call original method first
        if (originalUpdateFilterInputs) {
            originalUpdateFilterInputs.call(this, column, selectedOperator, firstInput, secondInput, firstFilterWrapper, secondFilterWrapper, logicSection, secondDropdown);
        }

        const isDateOnly = column.type === 'date';
        
        if (isDateOnly) {
            // Convert native date inputs to custom datepickers
            setTimeout(() => {
                this.convertDateInputsToCustom(firstInput, secondInput, selectedOperator);
            }, 0);
        }
    };

    /**
     * Convert native date inputs to custom datepickers
     */
    SnapGrid.prototype.convertDateInputsToCustom = function(firstInput, secondInput, selectedOperator) {
        // Convert first input if it's a date type
        if (firstInput && firstInput.type === 'date' && !firstInput._snapDatepicker) {
            this.convertSingleDateInput(firstInput);
        }

        // Convert second input if it's a date type and visible
        if (secondInput && secondInput.type === 'date' && !secondInput._snapDatepicker && 
            secondInput.parentElement.style.display !== 'none') {
            this.convertSingleDateInput(secondInput);
        }
    };

    /**
     * Convert a single date input to custom datepicker
     */
    SnapGrid.prototype.convertSingleDateInput = function(input) {
        if (!input || input._snapDatepicker) return;

        // Create wrapper
        const wrapper = document.createElement('div');
        wrapper.className = 'snap-grid-datepicker-wrapper';
        
        // Insert wrapper
        input.parentNode.insertBefore(wrapper, input);
        
        // Create datepicker
        const datepicker = new SnapDatepicker(wrapper, {
            value: input.value,
            placeholder: input.placeholder || 'Select date',
            closeOnSelect: true
        });

        // Sync values
        datepicker.on('change', (value) => {
            input.value = value;
            const changeEvent = new Event('change', { bubbles: true });
            input.dispatchEvent(changeEvent);
        });

        // Hide original input
        input.style.display = 'none';
        input._snapDatepicker = datepicker;
    };

    /**
     * Handle filter input changes for custom datepickers
     */
    SnapGrid.prototype.handleFilterInputChange = function(column, input) {
        // This method can be called when datepicker values change
        // to trigger filter updates in the grid
        console.log('📅 Custom datepicker filter change:', {
            column: column.field,
            value: input.value
        });
    };

    /**
     * Cleanup method for custom datepickers
     */
    SnapGrid.prototype.cleanupCustomDatepickers = function(container) {
        const dateInputs = container.querySelectorAll('input[type="date"]');
        
        dateInputs.forEach(input => {
            if (input._snapDatepicker) {
                input._snapDatepicker.destroy();
                input.style.display = '';
                delete input._snapDatepicker;
            }
        });
    };

    // Add CSS for grid-specific datepicker styling
    const gridDatepickerCSS = `
        .snap-grid-datepicker-wrapper {
            width: 100%;
        }
        
        .snap-grid-datepicker-wrapper .snap-datepicker-input {
            font-size: 12px;
            padding: 8px 32px 8px 8px;
            height: 32px;
            box-sizing: border-box;
        }
        
        .snap-grid-datepicker-wrapper .snap-datepicker-icon {
            right: 8px;
        }
        
        .snap-grid-datepicker-wrapper .snap-datepicker-dropdown {
            font-size: 12px;
            z-index: 10000;
        }
        
        /* Ensure datepicker dropdowns appear above grid menus */
        .snap-grid .snap-datepicker-dropdown {
            z-index: 10001;
        }
    `;

    // Inject CSS
    const style = document.createElement('style');
    style.textContent = gridDatepickerCSS;
    document.head.appendChild(style);

    // Auto-initialize when SnapGrid is used
    const originalSnapGridConstructor = window.SnapGrid;
    
    window.SnapGrid = function(container, options) {
        // Call original constructor
        const instance = new originalSnapGridConstructor(container, options);
        
        // Add initialization hook for custom datepickers
        const originalRender = instance.render;
        instance.render = function() {
            const result = originalRender.call(this);
            
            // Initialize custom datepickers after render
            setTimeout(() => {
                if (window.SnapDatepicker) {
                    this.initializeCustomDatepickers();
                }
            }, 100);
            
            return result;
        };
        
        // Add method to initialize custom datepickers
        instance.initializeCustomDatepickers = function() {
            const container = this.container;
            const dateInputs = container.querySelectorAll('input[type="date"]:not([data-converted])');
            
            dateInputs.forEach(input => {
                if (!input._snapDatepicker) {
                    DatepickerIntegration.replaceNativeInput(input);
                    input.setAttribute('data-converted', 'true');
                }
            });
        };
        
        return instance;
    };

    // Copy static properties
    Object.setPrototypeOf(window.SnapGrid, originalSnapGridConstructor);
    Object.assign(window.SnapGrid, originalSnapGridConstructor);

    console.log('✅ SnapGrid custom datepicker integration loaded');

})();
