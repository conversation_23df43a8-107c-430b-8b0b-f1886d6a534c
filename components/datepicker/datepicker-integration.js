/**
 * Datepicker Integration Helper
 * Provides utilities to replace native date inputs with custom SnapDatepicker
 * 
 * This module helps integrate the custom datepicker with existing components
 * while maintaining API compatibility with native date inputs.
 */

class DatepickerIntegration {
    /**
     * Replace a native date input with SnapDatepicker
     * @param {HTMLInputElement} nativeInput - The native date input to replace
     * @param {Object} options - Additional options for the datepicker
     * @returns {SnapDatepicker} The created datepicker instance
     */
    static replaceNativeInput(nativeInput, options = {}) {
        if (!nativeInput || nativeInput.type !== 'date') {
            console.warn('DatepickerIntegration: Input is not a native date input');
            return null;
        }

        // Get current properties from native input
        const currentValue = nativeInput.value;
        const placeholder = nativeInput.placeholder || 'Select date';
        const disabled = nativeInput.disabled;
        const readonly = nativeInput.readOnly;
        const className = nativeInput.className;

        // Create wrapper for the datepicker
        const wrapper = document.createElement('div');
        wrapper.className = className;
        
        // Insert wrapper before the native input
        nativeInput.parentNode.insertBefore(wrapper, nativeInput);
        
        // Create datepicker
        const datepicker = new SnapDatepicker(wrapper, {
            value: currentValue,
            placeholder: placeholder,
            disabled: disabled,
            readonly: readonly,
            ...options
        });

        // Copy event listeners from native input
        const events = ['change', 'input', 'focus', 'blur'];
        events.forEach(eventType => {
            datepicker.input.addEventListener(eventType, (e) => {
                // Create and dispatch the same event on the original input
                const event = new Event(eventType, { bubbles: true });
                Object.defineProperty(event, 'target', { value: nativeInput });
                Object.defineProperty(event, 'currentTarget', { value: nativeInput });
                nativeInput.dispatchEvent(event);
            });
        });

        // Sync values
        datepicker.on('change', (value) => {
            nativeInput.value = value;
            const changeEvent = new Event('change', { bubbles: true });
            nativeInput.dispatchEvent(changeEvent);
        });

        // Hide the native input
        nativeInput.style.display = 'none';
        
        // Store reference for cleanup
        nativeInput._snapDatepicker = datepicker;
        
        return datepicker;
    }

    /**
     * Replace native date inputs for date range (from/to inputs)
     * @param {HTMLInputElement} fromInput - The "from" date input
     * @param {HTMLInputElement} toInput - The "to" date input
     * @param {Object} options - Additional options
     * @returns {SnapDateRangePicker} The created date range picker instance
     */
    static replaceNativeDateRange(fromInput, toInput, options = {}) {
        if (!fromInput || !toInput) {
            console.warn('DatepickerIntegration: Both from and to inputs are required');
            return null;
        }

        // Get current values
        const fromValue = fromInput.value;
        const toValue = toInput.value;
        const fromPlaceholder = fromInput.placeholder || 'From date';
        const toPlaceholder = toInput.placeholder || 'To date';

        // Create wrapper
        const wrapper = document.createElement('div');
        wrapper.className = 'snap-daterange-integration-wrapper';
        
        // Insert wrapper before the first input
        fromInput.parentNode.insertBefore(wrapper, fromInput);
        
        // Create date range picker
        const dateRangePicker = new SnapDateRangePicker(wrapper, {
            startPlaceholder: fromPlaceholder,
            endPlaceholder: toPlaceholder,
            ...options
        });

        // Set initial values
        if (fromValue) dateRangePicker.setStartValue(fromValue);
        if (toValue) dateRangePicker.setEndValue(toValue);

        // Sync values with native inputs
        dateRangePicker.on('change', (values) => {
            fromInput.value = values.start || '';
            toInput.value = values.end || '';
            
            // Dispatch change events
            const fromEvent = new Event('change', { bubbles: true });
            const toEvent = new Event('change', { bubbles: true });
            fromInput.dispatchEvent(fromEvent);
            toInput.dispatchEvent(toEvent);
        });

        // Hide native inputs
        fromInput.style.display = 'none';
        toInput.style.display = 'none';
        
        // Store references
        fromInput._snapDateRangePicker = dateRangePicker;
        toInput._snapDateRangePicker = dateRangePicker;
        
        return dateRangePicker;
    }

    /**
     * Auto-replace all native date inputs in a container
     * @param {HTMLElement} container - Container to search for date inputs
     * @param {Object} options - Options for the datepickers
     * @returns {Array} Array of created datepicker instances
     */
    static autoReplaceInContainer(container, options = {}) {
        const dateInputs = container.querySelectorAll('input[type="date"]');
        const datepickers = [];

        dateInputs.forEach(input => {
            // Skip if already replaced
            if (input._snapDatepicker || input.style.display === 'none') {
                return;
            }

            const datepicker = this.replaceNativeInput(input, options);
            if (datepicker) {
                datepickers.push(datepicker);
            }
        });

        return datepickers;
    }

    /**
     * Restore native date input from custom datepicker
     * @param {HTMLInputElement} nativeInput - The original native input
     */
    static restoreNativeInput(nativeInput) {
        if (nativeInput._snapDatepicker) {
            nativeInput._snapDatepicker.destroy();
            nativeInput.style.display = '';
            delete nativeInput._snapDatepicker;
        }
    }

    /**
     * Get the datepicker instance associated with a native input
     * @param {HTMLInputElement} nativeInput - The native input
     * @returns {SnapDatepicker|null} The associated datepicker instance
     */
    static getDatepickerInstance(nativeInput) {
        return nativeInput._snapDatepicker || null;
    }

    /**
     * Update datepicker value programmatically
     * @param {HTMLInputElement} nativeInput - The native input
     * @param {string} value - The new value (YYYY-MM-DD format)
     */
    static updateValue(nativeInput, value) {
        if (nativeInput._snapDatepicker) {
            nativeInput._snapDatepicker.setValue(value);
        } else {
            nativeInput.value = value;
        }
    }

    /**
     * Enable/disable datepicker
     * @param {HTMLInputElement} nativeInput - The native input
     * @param {boolean} disabled - Whether to disable the datepicker
     */
    static setDisabled(nativeInput, disabled) {
        if (nativeInput._snapDatepicker) {
            nativeInput._snapDatepicker.options.disabled = disabled;
            nativeInput._snapDatepicker.input.disabled = disabled;
        } else {
            nativeInput.disabled = disabled;
        }
    }
}

// Global utility functions for easy access
window.DatepickerIntegration = DatepickerIntegration;

// Auto-initialization helper
window.initCustomDatepickers = function(container = document) {
    // Load CSS if not already loaded
    if (!document.querySelector('link[href*="snap-datepicker.css"]')) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'components/datepicker/snap-datepicker.css';
        document.head.appendChild(link);
    }

    // Auto-replace date inputs
    return DatepickerIntegration.autoReplaceInContainer(container);
};

// Initialize on DOM ready if auto-init is enabled
if (window.autoInitDatepickers !== false) {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.initCustomDatepickers();
        });
    } else {
        window.initCustomDatepickers();
    }
}
