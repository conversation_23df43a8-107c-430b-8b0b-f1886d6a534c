/**
 * SnapDatepicker - Custom Datepicker Component
 * Exact implementation matching the design specifications
 *
 * Features:
 * - Native date input API compatibility
 * - Custom calendar interface with precise styling
 * - Dark/light theme support
 * - Month/year dropdown navigation
 * - Keyboard navigation
 * - Date range support
 *
 * @version 2.0.0
 * <AUTHOR> Dashboard Team
 */

class SnapDatepicker {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.querySelector(container) : container;
        if (!this.container) {
            throw new Error('SnapDatepicker: Container element not found');
        }

        this.options = {
            value: '',
            placeholder: 'Select date',
            format: 'YYYY-MM-DD',
            minDate: null,
            maxDate: null,
            disabled: false,
            readonly: false,
            showToday: true,
            showClear: true,
            closeOnSelect: true,
            ...options
        };

        this.isOpen = false;
        this.selectedDate = null;
        this.viewDate = new Date();
        this.callbacks = {};
        this.monthNames = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];
        this.dayHeaders = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];

        this.init();
    }

    init() {
        this.createElements();
        this.bindEvents();
        this.setValue(this.options.value);
    }

    createElements() {
        // Main wrapper
        this.wrapper = document.createElement('div');
        this.wrapper.className = 'snap-datepicker-wrapper';

        // Input field
        this.input = document.createElement('input');
        this.input.type = 'text';
        this.input.className = 'snap-datepicker-input';
        this.input.placeholder = this.options.placeholder;
        this.input.readonly = true;

        // Calendar icon
        this.icon = document.createElement('div');
        this.icon.className = 'snap-datepicker-icon';
        this.icon.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
        `;

        // Calendar dropdown
        this.dropdown = document.createElement('div');
        this.dropdown.className = 'snap-datepicker-dropdown';
        this.dropdown.style.display = 'none';

        // Assemble input wrapper
        const inputWrapper = document.createElement('div');
        inputWrapper.className = 'snap-datepicker-input-wrapper';
        inputWrapper.appendChild(this.input);
        inputWrapper.appendChild(this.icon);

        this.wrapper.appendChild(inputWrapper);
        this.wrapper.appendChild(this.dropdown);
        this.container.appendChild(this.wrapper);

        this.createCalendar();
    }

    createCalendar() {
        this.dropdown.innerHTML = '';

        // Container
        const container = document.createElement('div');
        container.className = 'snap-datepicker-container';

        // Month/Year selector section
        const selectorSection = document.createElement('div');
        selectorSection.className = 'snap-datepicker-selector-section';

        // Month dropdown
        const monthDropdown = this.createDropdown('month', this.monthNames, this.viewDate.getMonth());

        // Year dropdown
        const currentYear = this.viewDate.getFullYear();
        const years = [];
        for (let i = currentYear - 50; i <= currentYear + 10; i++) {
            years.push(i.toString());
        }
        const yearDropdown = this.createDropdown('year', years, years.indexOf(currentYear.toString()));

        selectorSection.appendChild(monthDropdown);
        selectorSection.appendChild(yearDropdown);

        // Day headers row (separate section)
        const headerRow = document.createElement('div');
        headerRow.className = 'snap-datepicker-header-row';

        this.dayHeaders.forEach(day => {
            const dayHeader = document.createElement('div');
            dayHeader.className = 'snap-datepicker-day-header';
            dayHeader.textContent = day;
            headerRow.appendChild(dayHeader);
        });

        // Calendar body (separate section)
        const calendarBody = document.createElement('div');
        calendarBody.className = 'snap-datepicker-body';
        this.createCalendarDays(calendarBody);

        // Action buttons
        const buttonsSection = document.createElement('div');
        buttonsSection.className = 'snap-datepicker-buttons';

        if (this.options.showClear) {
            const clearBtn = document.createElement('button');
            clearBtn.className = 'snap-datepicker-clear-btn';
            clearBtn.textContent = 'Clear';
            clearBtn.onclick = () => this.clearDate();
            buttonsSection.appendChild(clearBtn);
        }

        if (this.options.showToday) {
            const todayBtn = document.createElement('button');
            todayBtn.className = 'snap-datepicker-today-btn';
            todayBtn.textContent = 'Today';
            todayBtn.onclick = () => this.selectToday();
            buttonsSection.appendChild(todayBtn);
        }

        container.appendChild(selectorSection);
        container.appendChild(headerRow);
        container.appendChild(calendarBody);
        container.appendChild(buttonsSection);
        this.dropdown.appendChild(container);
    }

    createDropdown(type, options, selectedIndex) {
        const dropdown = document.createElement('div');
        dropdown.className = `snap-datepicker-dropdown-${type} snap-dropdown`;

        const header = document.createElement('div');
        header.className = 'dropdown-header';

        const label = document.createElement('span');
        label.textContent = options[selectedIndex] || options[0];

        const icon = document.createElement('img');
        icon.src = './assets/dropdown-ic.svg';
        icon.alt = 'dropdown';

        header.appendChild(label);
        header.appendChild(icon);

        const menu = document.createElement('div');
        menu.className = 'dropdown-menu hidden';

        options.forEach((option, index) => {
            const item = document.createElement('div');
            item.className = 'dropdown-item';
            item.textContent = option;

            if (index === selectedIndex) {
                item.classList.add('selected');
            }

            item.onclick = (e) => {
                e.stopPropagation();
                this.selectDropdownOption(dropdown, index, option);
                this.closeDropdownMenus();
            };

            menu.appendChild(item);
        });

        dropdown.appendChild(header);
        dropdown.appendChild(menu);

        // Store dropdown data
        dropdown._type = type;
        dropdown._options = options;
        dropdown._selectedIndex = selectedIndex;

        header.onclick = () => this.toggleDropdownMenu(dropdown);

        return dropdown;
    }

    createCalendarDays(container) {
        const year = this.viewDate.getFullYear();
        const month = this.viewDate.getMonth();

        // First day of the month
        const firstDay = new Date(year, month, 1);

        // Start from Sunday of the week containing the first day
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - startDate.getDay());

        // Create rows for 6 weeks
        for (let week = 0; week < 6; week++) {
            const row = document.createElement('div');
            row.className = 'snap-datepicker-row';

            // Create 7 days for each week
            for (let day = 0; day < 7; day++) {
                const date = new Date(startDate);
                date.setDate(startDate.getDate() + (week * 7) + day);

                const dayElement = document.createElement('button');
                dayElement.className = 'snap-datepicker-day';
                dayElement.textContent = date.getDate();

                // Add state classes
                if (date.getMonth() !== month) {
                    dayElement.classList.add('disabled');
                } else if (this.selectedDate && this.isSameDate(date, this.selectedDate)) {
                    dayElement.classList.add('selected');
                } else {
                    dayElement.classList.add('default');
                }

                if (this.isDisabled(date)) {
                    dayElement.disabled = true;
                } else {
                    dayElement.onclick = () => this.selectDate(date);
                }

                row.appendChild(dayElement);
            }

            container.appendChild(row);
        }
    }

    bindEvents() {
        // Toggle dropdown on input click
        this.input.addEventListener('click', () => this.toggle());
        this.icon.addEventListener('click', () => this.toggle());

        // Close on outside click
        document.addEventListener('click', (e) => {
            if (!this.wrapper.contains(e.target)) {
                this.close();
            }
        });

        // Close dropdown menus on outside click
        document.addEventListener('click', (e) => {
            if (this.isOpen && !e.target.closest('.snap-datepicker-dropdown-month') &&
                !e.target.closest('.snap-datepicker-dropdown-year')) {
                this.closeDropdownMenus();
            }
        });

        // Keyboard navigation
        this.input.addEventListener('keydown', (e) => this.handleKeydown(e));
    }

    handleKeydown(e) {
        switch (e.key) {
            case 'Enter':
            case ' ':
                e.preventDefault();
                this.toggle();
                break;
            case 'Escape':
                this.close();
                break;
        }
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    open() {
        if (this.options.disabled || this.options.readonly) return;
        
        this.isOpen = true;
        this.dropdown.style.display = 'block';
        this.wrapper.classList.add('open');
        
        // Update calendar to current view
        this.createCalendar();
    }

    close() {
        this.isOpen = false;
        this.dropdown.style.display = 'none';
        this.wrapper.classList.remove('open');
    }

    selectDate(date) {
        this.selectedDate = new Date(date);
        this.updateInput();
        this.triggerChange();
        
        if (this.options.closeOnSelect) {
            this.close();
        } else {
            this.createCalendar(); // Refresh to show selection
        }
    }

    toggleDropdownMenu(dropdown) {
        const menu = dropdown.querySelector('.dropdown-menu');
        const isOpen = !menu.classList.contains('hidden');

        // Close all other dropdowns
        this.closeDropdownMenus();

        if (!isOpen) {
            dropdown.classList.add('focused');
            menu.classList.remove('hidden');
        }
    }

    closeDropdownMenus() {
        const openDropdowns = this.dropdown.querySelectorAll('.snap-dropdown.focused');
        openDropdowns.forEach(dropdown => {
            dropdown.classList.remove('focused');
            const menu = dropdown.querySelector('.dropdown-menu');
            if (menu) {
                menu.classList.add('hidden');
            }
        });
    }

    selectDropdownOption(dropdown, index, value) {
        dropdown._selectedIndex = index;
        const label = dropdown.querySelector('.dropdown-header span');
        label.textContent = value;

        // Update selected state in menu
        const items = dropdown.querySelectorAll('.dropdown-item');
        items.forEach((item, i) => {
            item.classList.toggle('selected', i === index);
        });

        if (dropdown._type === 'month') {
            this.viewDate.setMonth(index);
        } else if (dropdown._type === 'year') {
            this.viewDate.setFullYear(parseInt(value));
        }

        this.createCalendar();
    }

    selectToday() {
        this.selectDate(new Date());
    }

    clearDate() {
        this.selectedDate = null;
        this.updateInput();
        this.triggerChange();
        this.createCalendar();
    }

    updateInput() {
        if (this.selectedDate) {
            this.input.value = this.formatDate(this.selectedDate);
        } else {
            this.input.value = '';
        }
    }

    formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    isToday(date) {
        const today = new Date();
        return this.isSameDate(date, today);
    }

    isSameDate(date1, date2) {
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getDate() === date2.getDate();
    }

    isDisabled(date) {
        if (this.options.minDate && date < this.options.minDate) return true;
        if (this.options.maxDate && date > this.options.maxDate) return true;
        return false;
    }

    // Public API methods
    setValue(value) {
        if (value) {
            this.selectedDate = new Date(value);
            this.viewDate = new Date(this.selectedDate);
        } else {
            this.selectedDate = null;
        }
        this.updateInput();
    }

    getValue() {
        return this.selectedDate ? this.formatDate(this.selectedDate) : '';
    }

    on(event, callback) {
        this.callbacks[event] = callback;
    }

    triggerChange() {
        if (this.callbacks.change) {
            this.callbacks.change(this.getValue());
        }
        
        // Trigger native change event for compatibility
        const event = new Event('change', { bubbles: true });
        this.input.dispatchEvent(event);
    }

    destroy() {
        if (this.wrapper && this.wrapper.parentNode) {
            this.wrapper.parentNode.removeChild(this.wrapper);
        }
    }
}

/**
 * SnapDateRangePicker - Date Range Selection Component
 * Extends the basic datepicker for range selection
 */
class SnapDateRangePicker {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.querySelector(container) : container;
        if (!this.container) {
            throw new Error('SnapDateRangePicker: Container element not found');
        }

        this.options = {
            startPlaceholder: 'From date',
            endPlaceholder: 'To date',
            ...options
        };

        this.startDate = null;
        this.endDate = null;
        this.callbacks = {};

        this.init();
    }

    init() {
        this.createElements();
        this.bindEvents();
    }

    createElements() {
        this.wrapper = document.createElement('div');
        this.wrapper.className = 'snap-daterange-wrapper';

        // Start date picker
        const startWrapper = document.createElement('div');
        startWrapper.className = 'snap-daterange-input-wrapper';

        this.startPicker = new SnapDatepicker(startWrapper, {
            placeholder: this.options.startPlaceholder,
            closeOnSelect: true
        });

        // End date picker
        const endWrapper = document.createElement('div');
        endWrapper.className = 'snap-daterange-input-wrapper';

        this.endPicker = new SnapDatepicker(endWrapper, {
            placeholder: this.options.endPlaceholder,
            closeOnSelect: true
        });

        this.wrapper.appendChild(startWrapper);
        this.wrapper.appendChild(endWrapper);
        this.container.appendChild(this.wrapper);
    }

    bindEvents() {
        this.startPicker.on('change', (value) => {
            this.startDate = value;
            this.updateEndPickerMinDate();
            this.triggerChange();
        });

        this.endPicker.on('change', (value) => {
            this.endDate = value;
            this.updateStartPickerMaxDate();
            this.triggerChange();
        });
    }

    updateEndPickerMinDate() {
        if (this.startDate) {
            this.endPicker.options.minDate = new Date(this.startDate);
        }
    }

    updateStartPickerMaxDate() {
        if (this.endDate) {
            this.startPicker.options.maxDate = new Date(this.endDate);
        }
    }

    getStartValue() {
        return this.startPicker.getValue();
    }

    getEndValue() {
        return this.endPicker.getValue();
    }

    setStartValue(value) {
        this.startPicker.setValue(value);
        this.startDate = value;
        this.updateEndPickerMinDate();
    }

    setEndValue(value) {
        this.endPicker.setValue(value);
        this.endDate = value;
        this.updateStartPickerMaxDate();
    }

    on(event, callback) {
        this.callbacks[event] = callback;
    }

    triggerChange() {
        if (this.callbacks.change) {
            this.callbacks.change({
                start: this.getStartValue(),
                end: this.getEndValue()
            });
        }
    }

    destroy() {
        this.startPicker.destroy();
        this.endPicker.destroy();
        if (this.wrapper && this.wrapper.parentNode) {
            this.wrapper.parentNode.removeChild(this.wrapper);
        }
    }
}

// Export for use
window.SnapDatepicker = SnapDatepicker;
window.SnapDateRangePicker = SnapDateRangePicker;
